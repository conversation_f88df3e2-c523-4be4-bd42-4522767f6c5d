@echo off
title Blue Ocean Analyzer v3.0 Launcher

echo.
echo ========================================
echo    Blue Ocean Analyzer v3.0 Launcher
echo ========================================
echo.

:: Switch to F drive
echo Switching to F drive...
cd /d F:
if %errorlevel% neq 0 (
    echo Error: Cannot switch to F drive
    echo Please check if F drive is mounted
    pause
    exit /b 1
)

:: Switch to project directory
echo Switching to project directory...
cd "f:\zuomianwenjian\4.0\123"
if %errorlevel% neq 0 (
    echo Error: Cannot find project directory f:\zuomianwenjian\4.0\123
    echo Please check if the path is correct
    pause
    exit /b 1
)

:: Activate virtual environment
echo Activating virtual environment...
call "C:\Users\<USER>\lanhai310\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo Error: Cannot activate virtual environment
    echo Please check virtual environment path: C:\Users\<USER>\lanhai310
    pause
    exit /b 1
)

:: Check if Python file exists
if not exist "blue_ocean_analyzer_v2.py" (
    echo Error: Cannot find blue_ocean_analyzer_v2.py file
    echo Current directory: %cd%
    dir *.py
    pause
    exit /b 1
)

echo Environment check completed, starting program...
echo.

:: Start Python program
python blue_ocean_analyzer_v2.py

:: Handle program exit
echo.
echo ========================================
echo Program exited
echo ========================================
echo.

:: Show error information if program exited abnormally
if %errorlevel% neq 0 (
    echo Program exited abnormally, error code: %errorlevel%
    echo.
    echo Possible solutions:
    echo 1. Check if Python environment is correctly installed
    echo 2. Check if required Python packages are installed
    echo 3. Check if program files are complete
    echo.
) else (
    echo Program exited normally
)

pause

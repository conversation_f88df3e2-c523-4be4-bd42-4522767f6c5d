#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR处理模块 v3.0 - PaddleOCR 2.8.1 优化版
负责OCR识别和数据提取，支持GPU加速和批处理
"""

import os
import re
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

class OCRProcessor:
    """OCR处理器 v3.0 - 支持PaddleOCR 3.1.0新特性"""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        self.ocr = None
        self._confidence_threshold = 0.6  # 默认置信度阈值，可动态调整
        self.performance_stats = {
            'total_images': 0,
            'total_time': 0,
            'avg_time_per_image': 0,
            'gpu_enabled': False
        }

    def set_confidence_threshold(self, threshold):
        """设置置信度阈值"""
        self._confidence_threshold = threshold
        self.logger.debug(f"置信度阈值设置为: {threshold}")

    def get_confidence_threshold(self):
        """获取当前置信度阈值"""
        return self._confidence_threshold

    def init_ocr(self):
        """延迟初始化OCR - 支持PaddleOCR 3.1.0新特性"""
        if self.ocr is None:
            try:
                from paddleocr import PaddleOCR
                ocr_config = self.config_manager.get_ocr_config()

                # PaddleOCR 3.1.0 正确的参数配置
                init_params = {
                    # 根据官方文档，3.1.0使用这些参数名
                    'use_doc_orientation_classify': False,  # 文档方向分类
                    'use_doc_unwarping': False,  # 文档去扭曲
                    'use_textline_orientation': ocr_config.get('use_angle_cls', False),  # 文本行方向分类
                    'lang': ocr_config.get('lang', 'ch')
                }

                # 只添加非None的可选参数
                optional_params = {
                    'det_model_dir': ocr_config.get('det_model_dir'),
                    'rec_model_dir': ocr_config.get('rec_model_dir'),
                    'cls_model_dir': ocr_config.get('cls_model_dir')
                }

                for key, value in optional_params.items():
                    if value is not None:
                        init_params[key] = value

                # GPU控制通过环境变量
                gpu_enabled = ocr_config.get('use_gpu', False)
                if gpu_enabled:
                    os.environ['CUDA_VISIBLE_DEVICES'] = str(ocr_config.get('gpu_id', 0))
                else:
                    os.environ['CUDA_VISIBLE_DEVICES'] = '-1'  # 禁用GPU

                self.logger.info(f"初始化PaddleOCR 3.1.0，参数: {init_params}")
                self.ocr = PaddleOCR(**init_params)

                # 记录GPU状态
                self.performance_stats['gpu_enabled'] = gpu_enabled

                gpu_status = "GPU" if self.performance_stats['gpu_enabled'] else "CPU"
                self.logger.info(f"OCR初始化成功 - 运行模式: {gpu_status}")

            except Exception as e:
                self.logger.error(f"OCR初始化失败: {e}")
                raise
    
    def safe_float_convert(self, value: str) -> float:
        """安全的浮点数转换"""
        if not value or value == '-':
            return 0.0
        
        # 移除逗号和其他非数字字符（保留小数点）
        clean_value = re.sub(r'[^\d.]', '', str(value))
        
        try:
            return float(clean_value)
        except (ValueError, TypeError):
            return 0.0
    
    def standardize_range_format(self, range_str: str) -> str:
        """标准化范围格式"""
        if not range_str:
            return range_str
        
        # 统一使用 " ~ " 作为分隔符
        standardized = str(range_str).replace('~', ' ~ ').replace('-', ' ~ ')
        # 移除多余的空格
        standardized = re.sub(r'\s+', ' ', standardized).strip()
        
        return standardized
    

    
    def group_texts_by_rows(self, text_items: List[Dict]) -> List[List[str]]:
        """将文本按行分组 - 利用PaddleOCR的精确位置信息"""
        if not text_items:
            return []

        # 按y坐标排序
        sorted_items = sorted(text_items, key=lambda x: x['y_center'])

        rows = []
        current_row = []
        current_y = None

        # 动态计算y阈值，基于文本高度的平均值
        avg_height = sum(item['height'] for item in text_items) / len(text_items)
        y_threshold = max(avg_height * 0.8, 25)  # 自适应阈值，最小25像素

        self.logger.debug(f"行分组参数: 平均文本高度={avg_height:.1f}, Y阈值={y_threshold:.1f}")

        for item in sorted_items:
            if current_y is None or abs(item['y_center'] - current_y) <= y_threshold:
                current_row.append(item)
                current_y = item['y_center'] if current_y is None else (current_y + item['y_center']) / 2
            else:
                if current_row:
                    # 按x坐标排序当前行
                    current_row.sort(key=lambda x: x['x'])

                    # 智能处理复杂列结构（如垂直排列的天猫占比列）
                    processed_row = self._process_complex_columns(current_row, len(rows) + 1)
                    rows.append([item['text'] for item in processed_row])

                current_row = [item]
                current_y = item['y_center']

        # 添加最后一行
        if current_row:
            current_row.sort(key=lambda x: x['x'])
            processed_row = self._process_complex_columns(current_row, len(rows) + 1)
            rows.append([item['text'] for item in processed_row])

        self.logger.debug(f"行分组完成: 识别到 {len(rows)} 行数据")
        return rows

    def _process_complex_columns(self, row_items: List[Dict], row_number: int) -> List[Dict]:
        """处理复杂的列结构，如垂直排列的天猫占比列和缺失列"""
        # 如果是标准11列，检查垂直排列
        if len(row_items) == 11:
            # 检查最后两列是否需要特殊处理（垂直排列）
            last_two = row_items[-2:]
            other_cols = row_items[:-2]

            # 如果最后两列的x坐标很接近，说明是垂直排列，需要按y坐标排序
            if len(last_two) == 2:
                x_diff = abs(last_two[0]['x_center'] - last_two[1]['x_center'])
                avg_width = (last_two[0]['width'] + last_two[1]['width']) / 2

                if x_diff < avg_width * 0.5:  # x坐标差异小于平均宽度的50%，认为是垂直排列
                    last_two.sort(key=lambda x: x['y_center'])  # 按y坐标排序

                    if self.logger.isEnabledFor(logging.DEBUG):
                        self.logger.debug(f"第{row_number}行检测到垂直排列列: "
                                        f"[{last_two[0]['text']}, {last_two[1]['text']}]")

            return other_cols + last_two

        # 如果是10列，可能缺失了某一列，需要智能补齐
        elif len(row_items) == 10:
            # 分析列的x坐标分布，找出可能缺失的列位置
            processed_items = self._fix_missing_column(row_items, row_number)
            return processed_items

        # 其他情况直接返回
        return row_items

    def _fix_missing_column(self, row_items: List[Dict], row_number: int) -> List[Dict]:
        """修复缺失列的问题"""
        # 对于10列的情况，通常是缺失了搜索人气增长列（第3列）
        # 根据调试结果，我们需要在适当位置插入空值

        # 简单策略：如果第2列看起来像是支付转化率（包含%），
        # 说明缺失了搜索人气增长列，在第2列后插入空值
        if len(row_items) >= 3:
            # 检查第3列是否看起来像支付转化率
            third_col_text = row_items[2]['text']
            if '%' in third_col_text and ('~' in third_col_text or '.' in third_col_text):
                # 在第2列后插入一个虚拟的空列
                dummy_item = {
                    'text': '-',
                    'x': row_items[1]['x'] + (row_items[2]['x'] - row_items[1]['x']) / 2,
                    'x_center': row_items[1]['x_center'] + (row_items[2]['x_center'] - row_items[1]['x_center']) / 2,
                    'y_center': row_items[1]['y_center'],
                    'width': 10,
                    'height': row_items[1]['height'],
                    'confidence': 1.0,
                    'bbox': [[0, 0], [0, 0], [0, 0], [0, 0]]
                }

                result = row_items[:2] + [dummy_item] + row_items[2:]

                if self.logger.isEnabledFor(logging.DEBUG):
                    self.logger.debug(f"第{row_number}行: 在第2列后插入缺失的搜索人气增长列")

                return result

        return row_items

    def fix_ocr_errors(self, text: str) -> str:
        """修复常见的OCR识别错误"""
        # OCR常见错误修正映射
        ocr_fixes = {
            '% L ~ %': '5% ~ 7.5%',  # 常见的OCR错误
            '%S-': '-5%',            # 另一个常见错误
            '万~': '万 ~ ',          # 格式标准化
            '2万~4万': '2万 ~ 4万',   # 格式标准化
        }

        fixed_text = text
        for error, correction in ocr_fixes.items():
            fixed_text = fixed_text.replace(error, correction)

        return fixed_text

    def is_valid_product_name(self, text: str) -> bool:
        """判断是否为有效的产品名称"""
        if not text or len(text.strip()) < 2:  # 降低长度要求
            return False

        # 排除表头关键词
        header_keywords = [
            '相关搜索词', '相关搜素词', '搜索人气', '搜素人气', '支付转化率', '支付买家数',
            '需求供给比', '天猫商品点击占比', '天猫占比', '天猫商品点击占比○',
            '转化率', '买家数', '供给比', '点击占比', '产品名称', '产品分析'
        ]

        text_clean = text.strip()
        for keyword in header_keywords:
            if keyword in text_clean:
                return False

        # 特殊处理：如果包含产品关键词，直接认为是有效产品名
        product_keywords = [
            '防雨', '围裙', '雨衣', '防风', '过夜', '专业', '透明', '防水',
            '加厚', '妇女', '家用', '手洗', '衣服', '下雨天', '专用',
            '主妇', '成年', '电动车', '手套', '头盔', '方雨', '小头盔'
        ]
        for keyword in product_keywords:
            if keyword in text_clean:
                return True

        # 排除纯数字、百分比、符号等
        if re.match(r'^[\d\s%~\-+.,万]+$', text_clean):
            return False

        # 排除数字范围格式（如：4万~8万，1000~2500）- 但要更严格，避免误杀产品名
        if re.match(r'^\d+[万千]?\s*[~\-]\s*\d+[万千]?\s*$', text_clean):
            return False

        # 如果包含中文字符且长度合理，认为是产品名
        if re.search(r'[\u4e00-\u9fff]', text_clean) and len(text_clean) >= 2:
            # 额外检查：如果主要是数字范围但包含少量中文，仍然排除
            if re.match(r'^\d+[万千]?.*[~\-].*\d+[万千]?', text_clean):
                return False
            return True

        return False

    def _extract_text_items_v31(self, ocr_data) -> List[Dict]:
        """提取PaddleOCR 3.1.0格式的文本项 (字典格式)"""
        text_items = []
        confidence_threshold = self._confidence_threshold  # 使用动态阈值

        # 获取文本、置信度和位置信息 (字典访问)
        texts = ocr_data['rec_texts']
        scores = ocr_data['rec_scores']
        polys = ocr_data['rec_polys']

        for i, (text, score, poly) in enumerate(zip(texts, scores, polys)):
            if score >= confidence_threshold:
                # 计算边界框中心点和其他属性
                y_center = (poly[0][1] + poly[2][1]) / 2
                x_center = (poly[0][0] + poly[2][0]) / 2
                x_left = poly[0][0]

                # 计算文本框的宽度和高度
                width = abs(poly[2][0] - poly[0][0])
                height = abs(poly[2][1] - poly[0][1])

                text_items.append({
                    'text': text.strip(),
                    'confidence': score,
                    'y': y_center,  # 兼容旧版本
                    'x': x_left,    # 兼容旧版本
                    'x_center': x_center,
                    'y_center': y_center,
                    'x_left': x_left,
                    'width': width,
                    'height': height,
                    'bbox': poly
                })

        self.logger.debug(f"PaddleOCR 3.1.0格式: 提取到 {len(text_items)} 个高置信度文本项")
        return text_items

    def _extract_text_items_legacy(self, ocr_data) -> List[Dict]:
        """提取旧版本PaddleOCR格式的文本项"""
        text_items = []
        confidence_threshold = self._confidence_threshold  # 使用动态阈值

        for item in ocr_data:
            if len(item) >= 2 and isinstance(item[1], tuple):
                text, confidence = item[1]
                if confidence >= confidence_threshold:
                    bbox = item[0]
                    y_center = (bbox[0][1] + bbox[2][1]) / 2
                    x_left = bbox[0][0]

                    text_items.append({
                        'text': text.strip(),
                        'confidence': confidence,
                        'y_center': y_center,
                        'x_left': x_left,
                        'bbox': bbox
                    })

        self.logger.debug(f"旧版本格式: 提取到 {len(text_items)} 个高置信度文本项")
        return text_items

    def extract_product_data(self, ocr_result: List, image_path: str) -> List[Dict[str, Any]]:
        """从OCR结果中提取产品数据 - 支持PaddleOCR 3.1.0新格式"""
        if not ocr_result or not ocr_result[0]:
            return []

        image_name = os.path.basename(image_path)
        self.logger.debug(f"开始处理图片: {image_name}")

        # 适配PaddleOCR 3.1.0的新数据结构
        ocr_data = ocr_result[0]

        # 检查是否为新版本格式 (PaddleOCR 3.1.0使用字典格式)
        if isinstance(ocr_data, dict) and 'rec_texts' in ocr_data and 'rec_scores' in ocr_data and 'rec_polys' in ocr_data:
            # PaddleOCR 3.1.0格式
            text_items = self._extract_text_items_v31(ocr_data)
        else:
            # 旧版本格式
            text_items = self._extract_text_items_legacy(ocr_data)

        if not text_items:
            self.logger.warning(f"未提取到任何高置信度文本项: {image_name}")
            return []

        self.logger.info(f"OCR识别到 {len(text_items)} 个高质量文本项 (阈值: {self._confidence_threshold})")

        # 按行分组
        rows = self.group_texts_by_rows(text_items)

        if self.logger.isEnabledFor(logging.DEBUG):
            self.logger.debug(f"识别到 {len(rows)} 行数据:")
            for i, row in enumerate(rows, 1):
                self.logger.debug(f"第{i}行 ({len(row)}列): {row}")

        # 提取产品数据
        products = []
        valid_products_count = 0

        for i, row in enumerate(rows, 1):
            if not row:
                continue

            product_name_candidate = row[0].strip()

            # 检查是否为有效产品名称
            if not self.is_valid_product_name(product_name_candidate):
                self.logger.debug(f"跳过第{i}行: '{product_name_candidate}' (表头或无效)")
                continue

            valid_products_count += 1
            self.logger.debug(f"发现有效产品第{i}行: '{product_name_candidate}'")

            # 创建产品数据字典
            product = {
                "产品名称": product_name_candidate,
                "文件名": image_name,
                "行号": i,
                "原始列数": len(row)
            }

            # 智能OCR错误修复 - 利用3.1.0的高精度减少修复需求
            fixed_row = [self.fix_ocr_errors(cell) for cell in row]
            if fixed_row != row:
                self.logger.debug(f"第{i}行OCR修正: {len([j for j, (old, new) in enumerate(zip(row, fixed_row)) if old != new])} 处修正")
                row = fixed_row

            # 智能数据映射 - 支持多种列格式
            success = self._map_product_data(product, row, i)
            if success:
                products.append(product)
                self.logger.debug(f"成功解析产品: {product_name_candidate}")
            else:
                self.logger.warning(f"第{i}行数据映射失败，跳过")

        self.logger.info(f"图片 {image_name} 处理完成: 识别到 {len(products)} 个有效产品 "
                        f"(共 {valid_products_count} 个候选产品)")
        return products

    def _map_product_data(self, product: Dict[str, Any], row: List[str], row_number: int) -> bool:
        """智能数据映射 - 支持11列、10列和6列格式"""
        try:
            if len(row) >= 11:  # 11列格式：包含趋势数据
                # 智能识别第6列和第7列的数据类型来确定正确映射
                # 支付买家数通常是数字范围（如：9, 500~750, 10~50）
                # 买家数增长通常是百分比（如：130%, 5%, -30%）
                col5_val = row[5].strip()  # 第6列
                col6_val = row[6].strip()  # 第7列

                # 判断哪个是支付买家数（数字范围），哪个是买家数增长（百分比）
                if '%' in col5_val and ('~' in col6_val or col6_val.isdigit()):
                    # 第6列是百分比，第7列是数字范围
                    buyer_growth = col5_val
                    buyer_count = col6_val
                elif '%' in col6_val and ('~' in col5_val or col5_val.isdigit()):
                    # 第7列是百分比，第6列是数字范围
                    buyer_growth = col6_val
                    buyer_count = col5_val
                else:
                    # 默认映射（如果无法智能判断）
                    buyer_growth = col5_val
                    buyer_count = col6_val

                product.update({
                    "搜索人气": self.standardize_range_format(row[1]),
                    "搜索人气增长": row[2],
                    "支付转化率": self.standardize_range_format(row[3]),
                    "转化率增长": row[4],
                    "买家数增长": buyer_growth,
                    "支付买家数": self.standardize_range_format(buyer_count),
                    "需求供给比增长": row[7],  # 第8列是需求供给比增长
                    "需求供给比": self.safe_float_convert(row[8]),  # 第9列是需求供给比
                    "天猫占比": row[9],
                    "天猫占比增长": row[10],
                    "数据格式": "完整版(11列)"
                })

                self.logger.debug(f"智能映射: 买家数增长='{buyer_growth}', 支付买家数='{buyer_count}'")

                self.logger.debug(f"第{row_number}行: 11列完整数据映射成功")
                return True

            elif len(row) == 10:  # 10列格式：缺少一列数据，需要智能识别
                # 分析10列数据的结构，通常是缺少了搜索人气增长列
                # 修正后的10列格式：
                # [产品名称, 搜索人气, 支付转化率, 转化率增长, 支付买家数, 买家数增长, 需求供给比, 需求供给比增长, 天猫占比, 天猫占比增长]
                product.update({
                    "搜索人气": self.standardize_range_format(row[1]),
                    "搜索人气增长": "-",  # 缺失的列
                    "支付转化率": self.standardize_range_format(row[2]),
                    "转化率增长": row[3],
                    "支付买家数": self.standardize_range_format(row[4]),
                    "买家数增长": row[5],
                    "需求供给比": self.safe_float_convert(row[6]),  # 修正：第7列是需求供给比
                    "需求供给比增长": row[7],  # 修正：第8列是需求供给比增长
                    "天猫占比": row[8],
                    "天猫占比增长": row[9],
                    "数据格式": "部分版(10列)"
                })

                self.logger.debug(f"第{row_number}行: 10列部分数据映射成功")
                return True

            elif len(row) >= 6:  # 6列格式：无趋势数据
                # 标准6列映射：[产品名称, 搜索人气, 支付转化率, 支付买家数, 需求供给比, 天猫占比]
                product.update({
                    "搜索人气": self.standardize_range_format(row[1]),
                    "支付转化率": self.standardize_range_format(row[2]),
                    "支付买家数": self.standardize_range_format(row[3]),
                    "需求供给比": self.safe_float_convert(row[4]),
                    "天猫占比": row[5],
                    # 趋势数据默认值
                    "搜索人气增长": "-",
                    "转化率增长": "-",
                    "买家数增长": "-",
                    "需求供给比增长": "-",
                    "天猫占比增长": "-",
                    "数据格式": "基础版(6列)"
                })

                self.logger.debug(f"第{row_number}行: 6列基础数据映射成功")
                return True

            else:
                self.logger.warning(f"第{row_number}行: 列数不足 ({len(row)} < 6)，无法映射")
                return False

        except (ValueError, IndexError, TypeError) as e:
            self.logger.error(f"第{row_number}行数据映射错误: {e}, 行数据: {row}")
            return False
    
    def process_image(self, image_path: str) -> List[Dict[str, Any]]:
        """处理单张图片 - 使用PaddleOCR 3.1.0新API"""
        try:
            if self.ocr is None:
                self.init_ocr()

            start_time = time.time()

            # 使用PaddleOCR 3.1.0的predict方法（正确的参数格式）
            try:
                result = self.ocr.predict(input=image_path)
                self.logger.debug("使用predict方法成功")
            except AttributeError:
                # 如果predict方法不存在，回退到ocr方法
                result = self.ocr.ocr(image_path)
                self.logger.debug("回退到ocr方法")

            process_time = time.time() - start_time

            # 更新性能统计
            self.performance_stats['total_images'] += 1
            self.performance_stats['total_time'] += process_time
            self.performance_stats['avg_time_per_image'] = (
                self.performance_stats['total_time'] / self.performance_stats['total_images']
            )

            # 提取数据
            products_data = self.extract_product_data(result, image_path)

            self.logger.info(f"图片处理完成: {os.path.basename(image_path)}, "
                           f"耗时: {process_time:.2f}s, "
                           f"识别到 {len(products_data)} 个产品")

            return products_data

        except Exception as e:
            self.logger.error(f"处理图片失败 {image_path}: {e}")
            return []

    def process_images_batch(self, image_paths: List[str]) -> List[Dict[str, Any]]:
        """批量处理图片 - 利用PaddleOCR 3.1.0的批处理优化"""
        if not image_paths:
            return []

        try:
            if self.ocr is None:
                self.init_ocr()

            all_products = []
            batch_start_time = time.time()

            self.logger.info(f"开始批量处理 {len(image_paths)} 张图片")

            for i, image_path in enumerate(image_paths, 1):
                try:
                    products_data = self.process_image(image_path)
                    all_products.extend(products_data)

                    if i % 5 == 0:  # 每5张图片记录一次进度
                        elapsed = time.time() - batch_start_time
                        avg_time = elapsed / i
                        remaining = (len(image_paths) - i) * avg_time
                        self.logger.info(f"批处理进度: {i}/{len(image_paths)}, "
                                       f"预计剩余时间: {remaining:.1f}s")

                except Exception as e:
                    self.logger.error(f"批处理中图片失败 {image_path}: {e}")
                    continue

            total_time = time.time() - batch_start_time
            self.logger.info(f"批量处理完成: {len(image_paths)}张图片, "
                           f"总耗时: {total_time:.2f}s, "
                           f"识别到 {len(all_products)} 个产品")

            return all_products

        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            return []

    def process_images_batch_optimized(self, image_paths: List[str], batch_size: int = None) -> List[Dict[str, Any]]:
        """优化的批量处理图片 - 真正的批处理以提高GPU利用率"""
        if not image_paths:
            return []

        try:
            if self.ocr is None:
                self.init_ocr()

            # 获取批处理大小配置
            if batch_size is None:
                performance_config = self.config_manager.get_performance_config()
                batch_size = performance_config.get('batch_size', 8)

            all_products = []
            batch_start_time = time.time()
            total_images = len(image_paths)

            self.logger.info(f"开始优化批量处理 {total_images} 张图片，批处理大小: {batch_size}")

            # 分批处理图片
            for batch_start in range(0, total_images, batch_size):
                batch_end = min(batch_start + batch_size, total_images)
                batch_paths = image_paths[batch_start:batch_end]

                try:
                    # 真正的批处理OCR识别
                    batch_results = self._process_batch_ocr(batch_paths)

                    # 处理批处理结果
                    for image_path, ocr_result in zip(batch_paths, batch_results):
                        if ocr_result:
                            products_data = self.extract_product_data(ocr_result, image_path)
                            all_products.extend(products_data)

                    # 更新进度
                    processed = batch_end
                    elapsed = time.time() - batch_start_time
                    avg_time = elapsed / processed
                    remaining = (total_images - processed) * avg_time

                    self.logger.info(f"批处理进度: {processed}/{total_images} "
                                   f"({processed/total_images*100:.1f}%), "
                                   f"预计剩余: {remaining:.1f}s")

                except Exception as e:
                    self.logger.error(f"批处理失败 (batch {batch_start}-{batch_end}): {e}")
                    # 回退到单张处理
                    for image_path in batch_paths:
                        try:
                            products_data = self.process_image(image_path)
                            all_products.extend(products_data)
                        except Exception as single_e:
                            self.logger.error(f"单张处理也失败 {image_path}: {single_e}")

            total_time = time.time() - batch_start_time
            self.logger.info(f"优化批量处理完成: {total_images}张图片, "
                           f"总耗时: {total_time:.2f}s, "
                           f"识别到 {len(all_products)} 个产品, "
                           f"平均: {total_time/total_images:.2f}s/张")

            return all_products

        except Exception as e:
            self.logger.error(f"优化批量处理失败: {e}")
            return []

    def _process_batch_ocr(self, image_paths: List[str]) -> List:
        """执行真正的批处理OCR识别"""
        try:
            # 尝试使用PaddleOCR的批处理功能
            if hasattr(self.ocr, 'predict') and len(image_paths) > 1:
                # PaddleOCR 3.1.0 批处理方式
                results = []
                for image_path in image_paths:
                    result = self.ocr.predict(input=image_path)
                    results.append(result)
                return results
            else:
                # 回退到单张处理
                results = []
                for image_path in image_paths:
                    result = self.ocr.ocr(image_path)
                    results.append(result)
                return results

        except Exception as e:
            self.logger.error(f"批处理OCR识别失败: {e}")
            raise

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return self.performance_stats.copy()

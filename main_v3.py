#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蓝海产品分析器 v3.0 主程序入口
PaddleOCR 3.1.0 GPU优化版
"""

import os
import sys
import logging
import traceback
from pathlib import Path

def setup_environment():
    """设置运行环境"""
    # 添加当前目录到Python路径
    current_dir = Path(__file__).parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # 设置工作目录
    os.chdir(current_dir)

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        ('paddle', 'PaddlePaddle'),
        ('paddleocr', 'PaddleOCR'),
        ('PIL', 'Pillow'),
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('pandas', 'Pandas'),
        ('openpyxl', 'OpenPyXL'),
        ('tkinter', 'Tkinter')
    ]
    
    missing_packages = []
    installed_versions = {}
    
    for package, name in required_packages:
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'Unknown')
            installed_versions[name] = version
            print(f"✅ {name}: {version}")
        except ImportError:
            missing_packages.append(name)
            print(f"❌ {name}: 未安装")
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements_lanhai.txt")
        return False, {}
    
    return True, installed_versions

def check_gpu_support():
    """检查GPU支持"""
    try:
        import paddle
        if paddle.is_compiled_with_cuda():
            gpu_count = paddle.device.cuda.device_count()
            if gpu_count > 0:
                print(f"🚀 GPU支持: 检测到 {gpu_count} 个GPU设备")
                return True
            else:
                print("⚠️ GPU支持: CUDA可用但未检测到GPU设备")
                return False
        else:
            print("ℹ️ GPU支持: PaddlePaddle未编译CUDA支持")
            return False
    except Exception as e:
        print(f"⚠️ GPU检查失败: {e}")
        return False

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('analyzer_v3.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # 设置PaddleOCR日志级别
    logging.getLogger('ppocr').setLevel(logging.WARNING)
    logging.getLogger('paddle').setLevel(logging.WARNING)

def show_startup_info(versions, gpu_enabled):
    """显示启动信息"""
    print("\n" + "="*60)
    print("🌊 蓝海产品分析器 v3.0 - PaddleOCR 2.8.1 GPU优化版")
    print("="*60)
    
    print("\n📦 核心组件版本:")
    key_components = ['PaddlePaddle', 'PaddleOCR', 'NumPy', 'OpenCV']
    for component in key_components:
        if component in versions:
            print(f"   {component}: {versions[component]}")
    
    print(f"\n🚀 运行模式: {'GPU加速' if gpu_enabled else 'CPU模式'}")
    
    print("\n✨ v3.0 新特性:")
    print("   • GPU加速处理，速度提升3-5倍")
    print("   • 智能批处理，支持大量图片并行处理")
    print("   • 高精度识别，置信度阈值提升至0.8")
    print("   • 实时性能监控和统计")
    print("   • 自适应版面分析算法")
    
    print("\n🎯 使用建议:")
    if gpu_enabled:
        print("   • 建议启用批处理模式以获得最佳性能")
        print("   • 大批量图片处理时GPU优势明显")
    else:
        print("   • CPU模式下建议小批量处理")
        print("   • 考虑安装GPU版本以获得更好性能")
    
    print("\n" + "="*60)

def main():
    """主函数"""
    try:
        print("🚀 启动蓝海产品分析器 v3.0...")
        
        # 设置环境
        setup_environment()
        
        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)
        
        print("\n🔍 检查系统环境...")
        
        # 检查依赖
        deps_ok, versions = check_dependencies()
        if not deps_ok:
            input("\n按回车键退出...")
            return 1
        
        # 检查GPU支持
        gpu_enabled = check_gpu_support()
        
        # 显示启动信息
        show_startup_info(versions, gpu_enabled)
        
        print("\n🎬 启动图形界面...")
        
        # 导入并启动主程序
        from blue_ocean_analyzer_v2 import BlueOceanAnalyzerV3
        
        # 创建应用实例
        app = BlueOceanAnalyzerV3()
        
        # 记录启动信息
        logger.info(f"应用启动成功 - GPU: {gpu_enabled}")
        logger.info(f"PaddlePaddle: {versions.get('PaddlePaddle', 'Unknown')}")
        logger.info(f"PaddleOCR: {versions.get('PaddleOCR', 'Unknown')}")
        
        # 启动GUI主循环
        app.root.mainloop()
        
        logger.info("应用正常退出")
        return 0
        
    except ImportError as e:
        print(f"\n❌ 导入错误: {e}")
        print("请检查是否正确安装了所有依赖包")
        traceback.print_exc()
        input("\n按回车键退出...")
        return 1
        
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        traceback.print_exc()
        input("\n按回车键退出...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU优化测试脚本
用于测试不同批处理参数对GPU利用率的影响
"""

import os
import time
import json
import logging
from typing import List, Dict
from gpu_monitor import GPUMonitor
from config_manager import ConfigManager
from ocr_processor import OCRProcessor

class GPUOptimizationTester:
    """GPU优化测试器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config_manager = ConfigManager()
        self.gpu_monitor = GPUMonitor()
        self.test_results = []
        
    def test_batch_sizes(self, image_paths: List[str], batch_sizes: List[int] = None):
        """测试不同批处理大小对GPU利用率的影响
        
        Args:
            image_paths: 测试图片路径列表
            batch_sizes: 要测试的批处理大小列表
        """
        if batch_sizes is None:
            batch_sizes = [1, 4, 8, 12, 16, 20, 24]
            
        if not image_paths:
            self.logger.error("没有提供测试图片")
            return
            
        # 限制测试图片数量以节省时间
        test_images = image_paths[:min(20, len(image_paths))]
        
        print(f"\n🚀 开始GPU优化测试")
        print(f"测试图片数量: {len(test_images)}")
        print(f"测试批处理大小: {batch_sizes}")
        print("="*60)
        
        for batch_size in batch_sizes:
            print(f"\n📊 测试批处理大小: {batch_size}")
            result = self._test_single_batch_size(test_images, batch_size)
            self.test_results.append(result)
            
            # 打印当前测试结果
            print(f"  平均GPU利用率: {result['avg_gpu_utilization']:.1f}%")
            print(f"  最高GPU利用率: {result['max_gpu_utilization']:.1f}%")
            print(f"  处理时间: {result['processing_time']:.2f}秒")
            print(f"  平均每张: {result['time_per_image']:.2f}秒")
            
            # 短暂休息让GPU冷却
            time.sleep(2)
            
        # 打印最终报告
        self._print_optimization_report()
        
    def _test_single_batch_size(self, image_paths: List[str], batch_size: int) -> Dict:
        """测试单个批处理大小"""
        try:
            # 更新配置
            self._update_config_for_test(batch_size)
            
            # 创建OCR处理器
            ocr_processor = OCRProcessor(self.config_manager)
            
            # 开始GPU监控
            self.gpu_monitor.start_monitoring()
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行OCR处理
            if batch_size == 1:
                # 单张处理模式
                results = []
                for image_path in image_paths:
                    result = ocr_processor.process_image(image_path)
                    results.extend(result)
            else:
                # 批处理模式
                results = ocr_processor.process_images_batch_optimized(
                    image_paths, batch_size=batch_size
                )
            
            # 记录结束时间
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 停止GPU监控
            time.sleep(1)  # 等待最后的监控数据
            self.gpu_monitor.stop_monitoring()
            
            # 获取GPU统计信息
            gpu_stats = self.gpu_monitor.get_statistics()
            
            return {
                'batch_size': batch_size,
                'processing_time': processing_time,
                'time_per_image': processing_time / len(image_paths),
                'products_found': len(results),
                'avg_gpu_utilization': gpu_stats.get('avg_gpu_utilization', 0),
                'max_gpu_utilization': gpu_stats.get('max_gpu_utilization', 0),
                'avg_memory_utilization': gpu_stats.get('avg_memory_utilization', 0),
                'max_memory_utilization': gpu_stats.get('max_memory_utilization', 0),
                'samples_above_50_percent': gpu_stats.get('samples_above_50_percent', 0),
                'total_samples': gpu_stats.get('total_samples', 0)
            }
            
        except Exception as e:
            self.logger.error(f"测试批处理大小 {batch_size} 失败: {e}")
            return {
                'batch_size': batch_size,
                'error': str(e),
                'processing_time': 0,
                'avg_gpu_utilization': 0,
                'max_gpu_utilization': 0
            }
            
    def _update_config_for_test(self, batch_size: int):
        """为测试更新配置"""
        # 更新OCR配置
        ocr_config = self.config_manager.get_ocr_config()
        ocr_config['rec_batch_num'] = min(batch_size * 2, 32)  # OCR内部批处理大小
        
        # 更新性能配置
        performance_config = self.config_manager.get_performance_config()
        performance_config['batch_size'] = batch_size
        
        # 保存到配置管理器
        self.config_manager._config['ocr_config'].update(ocr_config)
        self.config_manager._config['performance_config'].update(performance_config)
        
    def _print_optimization_report(self):
        """打印优化报告"""
        if not self.test_results:
            print("没有测试结果")
            return
            
        print("\n" + "="*80)
        print("🎯 GPU优化测试报告")
        print("="*80)
        
        # 按GPU利用率排序
        sorted_results = sorted(
            [r for r in self.test_results if 'error' not in r],
            key=lambda x: x['avg_gpu_utilization'],
            reverse=True
        )
        
        if not sorted_results:
            print("所有测试都失败了")
            return
            
        print(f"{'批大小':<8} {'GPU利用率':<12} {'最高GPU':<10} {'处理时间':<10} {'每张时间':<10} {'产品数':<8}")
        print("-" * 80)
        
        for result in sorted_results:
            print(f"{result['batch_size']:<8} "
                  f"{result['avg_gpu_utilization']:.1f}%{'':<7} "
                  f"{result['max_gpu_utilization']:.1f}%{'':<5} "
                  f"{result['processing_time']:.2f}s{'':<5} "
                  f"{result['time_per_image']:.2f}s{'':<5} "
                  f"{result['products_found']:<8}")
        
        # 找出最佳配置
        best_result = sorted_results[0]
        print(f"\n🏆 推荐配置:")
        print(f"  批处理大小: {best_result['batch_size']}")
        print(f"  OCR批处理大小: {min(best_result['batch_size'] * 2, 32)}")
        print(f"  预期GPU利用率: {best_result['avg_gpu_utilization']:.1f}%")
        
        # 生成配置建议
        print(f"\n📝 配置文件建议 (config.json):")
        print(f'  "performance_config": {{')
        print(f'    "batch_size": {best_result["batch_size"]},')
        print(f'    "parallel_processing": true')
        print(f'  }},')
        print(f'  "ocr_config": {{')
        print(f'    "rec_batch_num": {min(best_result["batch_size"] * 2, 32)}')
        print(f'  }}')
        
        print("="*80)

def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 检查是否有测试图片
    test_images = []
    
    # 查找当前目录下的图片文件
    image_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif')
    for file in os.listdir('.'):
        if file.lower().endswith(image_extensions):
            test_images.append(file)
    
    if not test_images:
        print("❌ 当前目录下没有找到测试图片")
        print("请将一些测试图片放在当前目录下，支持的格式: .png, .jpg, .jpeg, .bmp, .tiff, .gif")
        return
    
    print(f"✅ 找到 {len(test_images)} 张测试图片")
    
    # 创建测试器并运行测试
    tester = GPUOptimizationTester()
    tester.test_batch_sizes(test_images)

if __name__ == "__main__":
    main()

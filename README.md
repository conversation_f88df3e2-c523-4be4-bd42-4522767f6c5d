# 🌊 蓝海产品分析器 v3.1

> 基于PaddleOCR 3.1.0的智能电商产品蓝海分析工具

## 📋 项目简介

蓝海产品分析器是一款专业的电商产品市场分析工具，通过OCR技术自动识别和分析产品数据截图，计算蓝海指数，帮助电商从业者快速发现市场机会。

### ✨ 核心特性

- 🚀 **高精度OCR识别**: 基于PaddleOCR 3.1.0 + PP-OCRv5模型
- 🎯 **智能数据提取**: 自动识别产品名称、搜索人气、转化率等关键指标
- 📊 **蓝海指数计算**: 基于多维度数据的智能评分算法
- 💻 **GPU加速支持**: 支持CUDA加速，处理速度提升3-5倍
- 🔄 **批处理模式**: 支持多图片批量处理
- 📈 **数据导出**: 支持Excel格式导出，便于进一步分析

### 🎯 适用场景

- 电商选品分析
- 市场竞争研究
- 产品蓝海挖掘
- 数据批量处理

## 🛠️ 技术栈

- **Python**: 3.8+
- **PaddlePaddle**: 3.1.0 (GPU版)
- **PaddleOCR**: 3.1.0 (PP-OCRv5模型)
- **GUI框架**: Tkinter
- **数据处理**: pandas, openpyxl
- **图像处理**: PIL, OpenCV

## 📦 环境要求

### 系统要求
- **操作系统**: Windows 10/11
- **Python**: 3.8+ (推荐3.9)
- **内存**: 8GB以上
- **显存**: 4GB以上 (GPU加速)

### GPU支持 (可选)
- **CUDA**: 11.8+
- **显卡**: NVIDIA GTX 1060 或更高

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
conda create -n lanhai310 python=3.9
conda activate lanhai310

# 或使用pip
python -m venv lanhai310
lanhai310\Scripts\activate  # Windows
```

### 2. 安装依赖

```bash
# 安装PaddlePaddle GPU版本
pip install paddlepaddle-gpu==3.1.0 -i https://pypi.tuna.tsinghua.edu.cn/simple

# 安装PaddleOCR
pip install paddleocr==3.1.0

# 安装其他依赖
pip install -r requirements_lanhai.txt
```

### 3. 运行程序

```bash
# 方法1: 使用启动脚本 (推荐)
python main_v3.py

# 方法2: 直接运行主程序
python blue_ocean_analyzer_v2.py
```

## 📖 使用指南

### 基本操作流程

1. **启动程序**: 运行 `main_v3.py` 或 `blue_ocean_analyzer_v2.py`
2. **选择图片**: 点击"选择图片"按钮，选择产品数据截图
3. **开始分析**: 点击"开始分析"按钮，等待OCR识别完成
4. **查看结果**: 在结果区域查看识别到的产品数据
5. **导出数据**: 点击"导出Excel"保存分析结果

### 支持的数据格式

- **11列完整格式**: 包含所有产品指标
- **10列部分格式**: 缺少部分指标
- **6列基础格式**: 基础产品信息

### 配置说明

主要配置文件 `config.json`:

```json
{
  "ocr_config": {
    "use_gpu": true,                    // 启用GPU加速
    "gpu_id": 0,                        // GPU设备ID
    "lang": "ch",                       // 识别语言
    "use_textline_orientation": false,  // 文本行方向分类
    "use_doc_orientation_classify": false, // 文档方向分类
    "use_doc_unwarping": false         // 文档去扭曲
  },
  "advanced_config": {
    "confidence_threshold": 0.6,        // 置信度阈值
    "enable_batch_processing": true     // 启用批处理
  }
}
```

## 🔧 高级功能

### GPU加速配置

```json
{
  "ocr_config": {
    "use_gpu": true,
    "gpu_id": 0,
    "rec_batch_num": 6
  }
}
```

### 性能测试

```bash
python performance_test.py
```

### 配置编辑器

```bash
python 配置编辑器.py
```

## 📊 性能指标

### 识别准确率
- **产品名称识别**: 95%+
- **数值数据识别**: 90%+
- **整体数据完整性**: 85%+

### 处理速度
- **CPU模式**: 2-3秒/图片
- **GPU模式**: 0.5-1秒/图片
- **批处理模式**: 提升50%+

## 🐛 故障排除

### 常见问题

1. **GPU不可用**
   - 检查CUDA安装
   - 验证显卡驱动
   - 自动降级到CPU模式

2. **识别准确率低**
   - 调整置信度阈值
   - 检查图片质量
   - 使用配置编辑器优化参数

3. **内存不足**
   - 减少批处理大小
   - 关闭其他程序
   - 使用CPU模式

### 日志文件

- `analyzer_v3.log`: 主程序日志
- `performance_test.log`: 性能测试日志

## 📁 项目结构

```
蓝海产品分析器 v3.1/
├── blue_ocean_analyzer_v2.py    # 主程序GUI
├── main_v3.py                   # 启动程序
├── ocr_processor.py             # OCR处理核心
├── config_manager.py            # 配置管理
├── score_calculator.py          # 评分计算
├── data_exporter.py             # 数据导出
├── data_importer.py             # 数据导入
├── performance_test.py          # 性能测试
├── 配置编辑器.py                # 配置编辑器
├── config.json                  # 主配置文件
├── requirements_lanhai.txt      # 依赖列表
├── README.md                    # 项目说明
├── 升级说明_v3.0.md            # 升级文档
├── 技术架构文档_v3.1.md        # 技术架构设计
├── 列映射技术文档_v3.1.md      # 列映射实现详解
└── 1/                          # 测试数据目录
```

## 🔄 版本历史

### v3.1 (2025-07-31)
- ✅ 升级到PaddleOCR 3.1.0
- ✅ 支持PP-OCRv5模型
- ✅ 完全兼容新API结构
- ✅ 智能列映射技术 (解决列对齐问题)
- ✅ 项目代码清理和优化
- ✅ 完整技术文档体系

### v3.0 (2025-07-30)
- ✅ 升级到PaddlePaddle 3.0.0
- ✅ GPU加速支持
- ✅ 批处理模式
- ✅ 性能监控

### v2.4 (历史版本)
- ✅ 基础OCR识别功能
- ✅ 蓝海指数计算
- ✅ Excel导出功能

## 📞 技术支持

如遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 运行 `python main_v3.py` 检查环境状态
3. 使用 `python performance_test.py` 测试性能

## 📚 技术文档

### 核心文档
- **[技术架构文档_v3.1.md](./技术架构文档_v3.1.md)** - 系统整体架构和技术实现
- **[列映射技术文档_v3.1.md](./列映射技术文档_v3.1.md)** - 智能列映射技术详解
- **[升级说明_v3.0.md](./升级说明_v3.0.md)** - 版本升级指南

### 快速导航
- **新手入门**: 阅读本README → 查看升级说明 → 运行测试
- **技术深入**: 技术架构文档 → 列映射技术文档
- **问题排查**: 列映射文档中的维护指南部分

## 📄 许可证

本项目仅供学习和研究使用。

---

**🎉 享受高效的蓝海产品分析体验！**

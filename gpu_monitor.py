#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU监控工具 - 用于监控GPU利用率和性能
帮助优化批处理参数以提高GPU利用率
"""

import time
import threading
import logging
from typing import Dict, List, Optional
import subprocess
import json

class GPUMonitor:
    """GPU性能监控器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.monitoring = False
        self.monitor_thread = None
        self.gpu_stats = []
        self.monitor_interval = 1.0  # 监控间隔（秒）
        
    def start_monitoring(self, duration: Optional[float] = None):
        """开始GPU监控
        
        Args:
            duration: 监控持续时间（秒），None表示持续监控直到手动停止
        """
        if self.monitoring:
            self.logger.warning("GPU监控已在运行中")
            return
            
        self.monitoring = True
        self.gpu_stats = []
        
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop, 
            args=(duration,),
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info("GPU监控已启动")
        
    def stop_monitoring(self):
        """停止GPU监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        self.logger.info("GPU监控已停止")
        
    def _monitor_loop(self, duration: Optional[float]):
        """监控循环"""
        start_time = time.time()
        
        while self.monitoring:
            try:
                # 检查是否超过监控时长
                if duration and (time.time() - start_time) >= duration:
                    break
                    
                # 获取GPU状态
                gpu_info = self._get_gpu_info()
                if gpu_info:
                    gpu_info['timestamp'] = time.time()
                    self.gpu_stats.append(gpu_info)
                    
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                self.logger.error(f"GPU监控出错: {e}")
                time.sleep(self.monitor_interval)
                
        self.monitoring = False
        
    def _get_gpu_info(self) -> Optional[Dict]:
        """获取GPU信息"""
        try:
            # 使用nvidia-smi获取GPU信息
            cmd = [
                'nvidia-smi', 
                '--query-gpu=utilization.gpu,utilization.memory,memory.used,memory.total,temperature.gpu,power.draw',
                '--format=csv,noheader,nounits'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if lines and lines[0]:
                    values = [v.strip() for v in lines[0].split(',')]
                    if len(values) >= 6:
                        return {
                            'gpu_utilization': float(values[0]) if values[0] != 'N/A' else 0,
                            'memory_utilization': float(values[1]) if values[1] != 'N/A' else 0,
                            'memory_used': float(values[2]) if values[2] != 'N/A' else 0,
                            'memory_total': float(values[3]) if values[3] != 'N/A' else 0,
                            'temperature': float(values[4]) if values[4] != 'N/A' else 0,
                            'power_draw': float(values[5]) if values[5] != 'N/A' else 0
                        }
            
        except Exception as e:
            self.logger.debug(f"获取GPU信息失败: {e}")
            
        return None
        
    def get_statistics(self) -> Dict:
        """获取监控统计信息"""
        if not self.gpu_stats:
            return {
                'message': '暂无监控数据',
                'avg_gpu_utilization': 0,
                'max_gpu_utilization': 0,
                'avg_memory_utilization': 0,
                'max_memory_utilization': 0
            }
            
        gpu_utils = [stat['gpu_utilization'] for stat in self.gpu_stats]
        mem_utils = [stat['memory_utilization'] for stat in self.gpu_stats]
        
        return {
            'total_samples': len(self.gpu_stats),
            'duration': self.gpu_stats[-1]['timestamp'] - self.gpu_stats[0]['timestamp'],
            'avg_gpu_utilization': sum(gpu_utils) / len(gpu_utils),
            'max_gpu_utilization': max(gpu_utils),
            'min_gpu_utilization': min(gpu_utils),
            'avg_memory_utilization': sum(mem_utils) / len(mem_utils),
            'max_memory_utilization': max(mem_utils),
            'min_memory_utilization': min(mem_utils),
            'samples_above_50_percent': len([u for u in gpu_utils if u > 50]),
            'samples_above_80_percent': len([u for u in gpu_utils if u > 80])
        }
        
    def print_statistics(self):
        """打印监控统计信息"""
        stats = self.get_statistics()
        
        if 'message' in stats:
            print(stats['message'])
            return
            
        print("\n" + "="*60)
        print("🚀 GPU性能监控报告")
        print("="*60)
        print(f"监控时长: {stats['duration']:.1f} 秒")
        print(f"采样次数: {stats['total_samples']} 次")
        print()
        print("📊 GPU利用率统计:")
        print(f"  平均利用率: {stats['avg_gpu_utilization']:.1f}%")
        print(f"  最高利用率: {stats['max_gpu_utilization']:.1f}%")
        print(f"  最低利用率: {stats['min_gpu_utilization']:.1f}%")
        print(f"  >50%的时间: {stats['samples_above_50_percent']}/{stats['total_samples']} ({stats['samples_above_50_percent']/stats['total_samples']*100:.1f}%)")
        print(f"  >80%的时间: {stats['samples_above_80_percent']}/{stats['total_samples']} ({stats['samples_above_80_percent']/stats['total_samples']*100:.1f}%)")
        print()
        print("💾 显存利用率统计:")
        print(f"  平均利用率: {stats['avg_memory_utilization']:.1f}%")
        print(f"  最高利用率: {stats['max_memory_utilization']:.1f}%")
        print(f"  最低利用率: {stats['min_memory_utilization']:.1f}%")
        print()
        
        # 给出优化建议
        avg_gpu = stats['avg_gpu_utilization']
        if avg_gpu < 30:
            print("💡 优化建议:")
            print("  - GPU利用率较低，建议增加批处理大小")
            print("  - 可以尝试将 rec_batch_num 增加到 20-32")
            print("  - 可以尝试将 batch_size 增加到 16-24")
        elif avg_gpu < 60:
            print("💡 优化建议:")
            print("  - GPU利用率中等，可以适当增加批处理大小")
            print("  - 可以尝试将 rec_batch_num 增加到 12-20")
        else:
            print("✅ GPU利用率良好！")
            
        print("="*60)

def test_gpu_monitor():
    """测试GPU监控功能"""
    monitor = GPUMonitor()
    
    print("开始GPU监控测试（10秒）...")
    monitor.start_monitoring(duration=10)
    
    # 等待监控完成
    time.sleep(11)
    
    # 打印统计信息
    monitor.print_statistics()

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    test_gpu_monitor()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蓝海产品分析器 v3.1 项目状态检查工具
检查项目完整性、依赖状态和功能可用性
"""

import os
import sys
import json
import importlib
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)

def print_status(item, status, details=""):
    """打印状态信息"""
    status_icon = "✅" if status else "❌"
    print(f"{status_icon} {item}")
    if details:
        print(f"   {details}")

def check_core_files():
    """检查核心文件"""
    print_header("🔍 核心文件检查")
    
    core_files = {
        'blue_ocean_analyzer_v2.py': '主程序GUI',
        'main_v3.py': '启动程序',
        'ocr_processor.py': 'OCR处理核心',
        'config_manager.py': '配置管理',
        'score_calculator.py': '评分计算',
        'data_exporter.py': '数据导出',
        'data_importer.py': '数据导入',
        'config.json': '配置文件',
        'requirements_lanhai.txt': '依赖列表'
    }
    
    all_present = True
    for file_name, description in core_files.items():
        exists = os.path.exists(file_name)
        print_status(f"{file_name} ({description})", exists)
        if not exists:
            all_present = False
    
    return all_present

def check_documentation():
    """检查文档文件"""
    print_header("📚 文档文件检查")
    
    doc_files = {
        'README.md': '项目说明文档',
        '升级说明_v3.0.md': '升级说明文档',
        '技术架构文档_v3.1.md': '技术架构文档',
        '项目清理总结.md': '清理总结文档'
    }
    
    all_present = True
    for file_name, description in doc_files.items():
        exists = os.path.exists(file_name)
        print_status(f"{file_name} ({description})", exists)
        if not exists:
            all_present = False
    
    return all_present

def check_test_data():
    """检查测试数据"""
    print_header("🖼️ 测试数据检查")
    
    test_dir = Path("1")
    if test_dir.exists():
        image_files = list(test_dir.glob("*.png")) + list(test_dir.glob("*.jpg"))
        print_status(f"测试图片目录", True, f"找到 {len(image_files)} 个图片文件")
        
        for img_file in image_files:
            print(f"   📷 {img_file.name}")
        
        return len(image_files) > 0
    else:
        print_status("测试图片目录", False, "目录不存在")
        return False

def check_dependencies():
    """检查Python依赖"""
    print_header("📦 依赖包检查")
    
    required_packages = [
        ('paddlepaddle-gpu', 'PaddlePaddle GPU版'),
        ('paddleocr', 'PaddleOCR'),
        ('pandas', '数据处理'),
        ('openpyxl', 'Excel处理'),
        ('Pillow', '图像处理'),
        ('tkinter', 'GUI框架')
    ]
    
    all_available = True
    for package, description in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            else:
                importlib.import_module(package.replace('-', '_'))
            print_status(f"{package} ({description})", True)
        except ImportError:
            print_status(f"{package} ({description})", False, "未安装或不可用")
            all_available = False
    
    return all_available

def check_config_file():
    """检查配置文件"""
    print_header("⚙️ 配置文件检查")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查关键配置项
        required_sections = ['ocr_config', 'advanced_config']
        all_valid = True
        
        for section in required_sections:
            if section in config:
                print_status(f"配置节 [{section}]", True)
            else:
                print_status(f"配置节 [{section}]", False, "缺失")
                all_valid = False
        
        # 检查OCR配置
        if 'ocr_config' in config:
            ocr_config = config['ocr_config']
            gpu_enabled = ocr_config.get('use_gpu', False)
            print(f"   🚀 GPU加速: {'启用' if gpu_enabled else '禁用'}")
            print(f"   🌐 识别语言: {ocr_config.get('lang', 'ch')}")
        
        return all_valid
        
    except Exception as e:
        print_status("config.json", False, f"读取失败: {e}")
        return False

def check_cleaned_files():
    """检查是否还有需要清理的文件"""
    print_header("🧹 清理状态检查")
    
    # 检查不应该存在的文件
    unwanted_patterns = [
        'debug_*.py',
        'test_*.py',
        '*.log',
        '__pycache__'
    ]
    
    unwanted_found = []
    for pattern in unwanted_patterns:
        if '*' in pattern:
            import glob
            matches = glob.glob(pattern)
            unwanted_found.extend(matches)
        else:
            if os.path.exists(pattern):
                unwanted_found.append(pattern)
    
    if unwanted_found:
        print_status("项目清理状态", False, f"发现 {len(unwanted_found)} 个需要清理的文件")
        for file in unwanted_found:
            print(f"   🗑️ {file}")
        return False
    else:
        print_status("项目清理状态", True, "项目已完全清理")
        return True

def check_version_consistency():
    """检查版本一致性"""
    print_header("🔢 版本一致性检查")
    
    version_files = {
        'README.md': 'v3.1',
        '升级说明_v3.0.md': 'v3.1',
        '技术架构文档_v3.1.md': 'v3.1'
    }
    
    all_consistent = True
    for file_name, expected_version in version_files.items():
        if os.path.exists(file_name):
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if expected_version in content:
                    print_status(f"{file_name}", True, f"包含版本 {expected_version}")
                else:
                    print_status(f"{file_name}", False, f"未找到版本 {expected_version}")
                    all_consistent = False
            except Exception as e:
                print_status(f"{file_name}", False, f"读取失败: {e}")
                all_consistent = False
        else:
            print_status(f"{file_name}", False, "文件不存在")
            all_consistent = False
    
    return all_consistent

def generate_summary(results):
    """生成总结报告"""
    print_header("📊 项目状态总结")
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"通过率: {passed_checks/total_checks*100:.1f}%")
    
    if passed_checks == total_checks:
        print("\n🎉 恭喜！项目状态完美，所有检查都通过了！")
        print("✨ 蓝海产品分析器 v3.1 已准备就绪！")
    else:
        print(f"\n⚠️ 发现 {total_checks - passed_checks} 个问题需要解决")
        print("请根据上述检查结果进行相应的修复")

def main():
    """主函数"""
    print("🌊 蓝海产品分析器 v3.1 项目状态检查")
    print("=" * 60)
    
    # 执行各项检查
    results = {
        '核心文件': check_core_files(),
        '文档文件': check_documentation(),
        '测试数据': check_test_data(),
        '依赖包': check_dependencies(),
        '配置文件': check_config_file(),
        '清理状态': check_cleaned_files(),
        '版本一致性': check_version_consistency()
    }
    
    # 生成总结
    generate_summary(results)

if __name__ == "__main__":
    main()
